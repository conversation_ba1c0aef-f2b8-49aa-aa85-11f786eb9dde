#!/bin/bash

# Optimized build script for TagTok backend
# This script implements various optimization strategies for faster builds

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCKER_BUILDKIT=1
COMPOSE_DOCKER_CLI_BUILD=1
BUILD_CACHE_DIR="$HOME/.docker-cache/tagtok"

echo -e "${BLUE}🚀 Starting optimized TagTok build...${NC}"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if Docker BuildKit is enabled
if [ "$DOCKER_BUILDKIT" != "1" ]; then
    print_warning "Docker BuildKit not enabled. Enabling for better caching..."
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
fi

# Create cache directory
mkdir -p "$BUILD_CACHE_DIR"

# Check for existing images to use as cache
print_status "Checking for existing images to use as build cache..."

# Pull base image if not present
if ! docker image inspect python:3.11-slim >/dev/null 2>&1; then
    print_status "Pulling Python base image..."
    docker pull python:3.11-slim
fi

# Build with optimizations
print_status "Building backend with optimizations..."

# Use BuildKit with cache mounts and multi-stage caching
DOCKER_BUILDKIT=1 docker-compose build \
    --build-arg BUILDKIT_INLINE_CACHE=1 \
    --build-arg INSTALL_DEV=false \
    backend

if [ $? -eq 0 ]; then
    print_status "Backend build completed successfully!"
else
    print_error "Backend build failed!"
    exit 1
fi

# Optional: Build development version
if [ "$1" = "--dev" ]; then
    print_status "Building development version with dev dependencies..."
    DOCKER_BUILDKIT=1 docker-compose build \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        --build-arg INSTALL_DEV=true \
        backend
fi

# Clean up dangling images to save space
print_status "Cleaning up dangling images..."
docker image prune -f >/dev/null 2>&1 || true

# Show final image size
IMAGE_SIZE=$(docker image inspect tagtok-backend:latest --format='{{.Size}}' 2>/dev/null || echo "0")
if [ "$IMAGE_SIZE" != "0" ]; then
    SIZE_MB=$((IMAGE_SIZE / 1024 / 1024))
    print_status "Final backend image size: ${SIZE_MB}MB"
fi

echo -e "${GREEN}🎉 Build optimization complete!${NC}"

# Show build time recommendations
echo -e "${BLUE}💡 Build time optimization tips:${NC}"
echo "  • Use 'docker-compose build --parallel' for multiple services"
echo "  • Run './scripts/build-optimized.sh --dev' for development builds"
echo "  • Use 'docker system prune' periodically to clean up build cache"
echo "  • Consider using a Docker registry for team development"
