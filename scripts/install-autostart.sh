#!/bin/bash

# Script to install TagTok as a systemd service for auto-start on boot

echo "Installing TagTok auto-start service..."

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "Please run this script as a regular user (not root)"
    echo "The script will use sudo when needed"
    exit 1
fi

# Check if docker and docker compose are available
if ! command -v docker &> /dev/null; then
    echo "Docker is not installed or not in PATH"
    exit 1
fi

if ! docker compose version &> /dev/null; then
    echo "Docker Compose is not available"
    exit 1
fi

# Check if user is in docker group
if ! groups $USER | grep -q docker; then
    echo "User $USER is not in the docker group"
    echo "Please add user to docker group: sudo usermod -aG docker $USER"
    echo "Then log out and log back in"
    exit 1
fi

# Copy the service file to systemd directory
echo "Installing systemd service..."
sudo cp /home/<USER>/tagTok/scripts/tagtok.service /etc/systemd/system/

# Reload systemd daemon
echo "Reloading systemd daemon..."
sudo systemctl daemon-reload

# Enable the service
echo "Enabling TagTok service..."
sudo systemctl enable tagtok.service

# Check service status
echo "Service status:"
sudo systemctl status tagtok.service --no-pager

echo ""
echo "Installation completed!"
echo ""
echo "Commands you can use:"
echo "  sudo systemctl start tagtok    # Start the service"
echo "  sudo systemctl stop tagtok     # Stop the service"
echo "  sudo systemctl status tagtok   # Check service status"
echo "  sudo systemctl disable tagtok  # Disable auto-start"
echo ""
echo "The service will now automatically start on boot."
echo "To start it now, run: sudo systemctl start tagtok"
