#!/bin/bash

# <PERSON><PERSON>t to clean up orphaned docker-pr processes that block ports
# Run this script if you encounter "address already in use" errors

echo "Cleaning up orphaned docker-pr processes..."

# Define the ports used by tagTok
PORTS=(80 3000 8000 11434)

for port in "${PORTS[@]}"; do
    echo "Checking port $port..."
    
    # Find processes using the port
    PIDS=$(sudo lsof -ti :$port 2>/dev/null)
    
    if [ ! -z "$PIDS" ]; then
        echo "Found processes using port $port: $PIDS"
        
        # Check if they are docker-pr processes
        for pid in $PIDS; do
            PROCESS_NAME=$(ps -p $pid -o comm= 2>/dev/null)
            if [ "$PROCESS_NAME" = "docker-pr" ]; then
                echo "Killing orphaned docker-pr process $pid on port $port"
                sudo kill -9 $pid
            else
                echo "Process $pid ($PROCESS_NAME) is not docker-pr, skipping"
            fi
        done
    else
        echo "Port $port is free"
    fi
done

echo "Port cleanup completed!"

# Optional: Clean up any remaining docker networks and containers
echo "Cleaning up docker system..."
docker system prune -f

echo "Cleanup script finished!"
