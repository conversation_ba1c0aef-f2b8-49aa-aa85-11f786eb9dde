[Unit]
Description=TagTok Docker Compose Application
Requires=docker.service
After=docker.service
Wants=network-online.target
After=network-online.target

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=/home/<USER>/tagTok
ExecStartPre=/bin/bash /home/<USER>/tagTok/scripts/cleanup-docker-ports.sh
ExecStart=/usr/bin/docker compose up -d
ExecStop=/usr/bin/docker compose down
TimeoutStartSec=300
User=acel
Group=acel

[Install]
WantedBy=multi-user.target
