#!/bin/bash

# Quick start script for TagTok that handles port cleanup

echo "Starting TagTok..."

# Change to the TagTok directory
cd /home/<USER>/tagTok

# Clean up any orphaned docker-pr processes
echo "Cleaning up ports..."
./scripts/cleanup-docker-ports.sh

# Start the application
echo "Starting Docker Compose services..."
docker compose up -d

# Show status
echo ""
echo "Service status:"
docker compose ps

echo ""
echo "TagTok should now be available at:"
echo "  Frontend: http://localhost:3000"
echo "  Backend API: http://localhost:8000"
echo "  Nginx (main): http://localhost:80"
