#!/bin/bash

# Quick development setup script for TagTok
# This script sets up the development environment with optimized builds

set -e

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}🔧 Setting up TagTok development environment...${NC}"

# Check if .env exists, if not copy from example
if [ ! -f .env ]; then
    echo -e "${YELLOW}📝 Creating .env file from example...${NC}"
    cp .env.example .env
    echo -e "${GREEN}✅ .env file created. Please review and modify as needed.${NC}"
fi

# Create necessary directories
echo -e "${BLUE}📁 Creating necessary directories...${NC}"
mkdir -p data/{videos,db,transcripts,logs}
mkdir -p scripts

# Set up Docker BuildKit
echo -e "${BLUE}🐳 Configuring Docker BuildKit...${NC}"
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# Add to shell profile if not already there
SHELL_PROFILE=""
if [ -f ~/.bashrc ]; then
    SHELL_PROFILE=~/.bashrc
elif [ -f ~/.zshrc ]; then
    SHELL_PROFILE=~/.zshrc
fi

if [ -n "$SHELL_PROFILE" ]; then
    if ! grep -q "DOCKER_BUILDKIT=1" "$SHELL_PROFILE"; then
        echo "export DOCKER_BUILDKIT=1" >> "$SHELL_PROFILE"
        echo "export COMPOSE_DOCKER_CLI_BUILD=1" >> "$SHELL_PROFILE"
        echo -e "${GREEN}✅ Docker BuildKit environment variables added to $SHELL_PROFILE${NC}"
    fi
fi

# Build optimized containers
echo -e "${BLUE}🚀 Building optimized containers...${NC}"
./scripts/build-optimized.sh --dev

# Start services
echo -e "${BLUE}🎬 Starting services...${NC}"
docker-compose up -d

echo -e "${GREEN}🎉 Development environment setup complete!${NC}"
echo ""
echo -e "${BLUE}📋 Next steps:${NC}"
echo "  • Frontend: http://localhost:3001"
echo "  • Backend API: http://localhost:8080"
echo "  • API Docs: http://localhost:8080/docs"
echo "  • GPU Status: http://localhost:8080/gpu-status"
echo ""
echo -e "${BLUE}💡 Development commands:${NC}"
echo "  • Fast rebuild: ./scripts/build-optimized.sh"
echo "  • View logs: docker-compose logs -f backend"
echo "  • Stop services: docker-compose down"
echo "  • Clean rebuild: docker-compose down && docker system prune -f && ./scripts/build-optimized.sh"
