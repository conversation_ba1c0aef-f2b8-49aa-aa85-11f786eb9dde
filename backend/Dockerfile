# Multi-stage build for optimized caching and smaller final image
FROM python:3.11-slim AS base

# Set environment variables for Python optimization
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PIP_DEFAULT_TIMEOUT=100

# Install system dependencies including Intel GPU support
RUN apt-get update && apt-get install -y \
    # Essential tools
    curl \
    wget \
    # Build tools (needed for some Python packages)
    build-essential \
    # Media processing
    ffmpeg \
    # Intel GPU support
    intel-media-va-driver \
    vainfo \
    intel-gpu-tools \
    # Cleanup
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create app user for security
RUN useradd --create-home --shell /bin/bash app

# Set working directory
WORKDIR /app

# Stage 1: Install core dependencies (cached separately)
FROM base AS core-deps
COPY requirements.txt .
RUN pip install --user -r requirements.txt

# Stage 2: Install ML dependencies (cached separately)
FROM core-deps AS ml-deps
COPY requirements-ml.txt .
RUN pip install --user -r requirements-ml.txt

# Stage 3: Development dependencies (optional)
FROM ml-deps AS dev-deps
COPY requirements-dev.txt .
ARG INSTALL_DEV=false
RUN if [ "$INSTALL_DEV" = "true" ]; then pip install --user -r requirements-dev.txt; fi

# Final stage: Application
FROM base AS final

# Copy installed packages from previous stages
COPY --from=dev-deps /root/.local /root/.local

# Add local bin to PATH
ENV PATH=/root/.local/bin:$PATH

# Create necessary directories
RUN mkdir -p videos db transcripts logs \
    && chown -R app:app /app

# Copy application code (this layer changes most frequently)
COPY --chown=app:app . .

# Switch to app user
USER app

# Set default port
ENV BACKEND_INTERNAL_PORT=8000

# Expose port
EXPOSE $BACKEND_INTERNAL_PORT

# Health check with timeout optimization
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:${BACKEND_INTERNAL_PORT}/health || exit 1

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
