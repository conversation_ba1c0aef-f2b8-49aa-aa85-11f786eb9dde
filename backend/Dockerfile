FROM python:3.11-slim

# Install system dependencies including Intel GPU support
RUN apt-get update && apt-get install -y \
    ffmpeg \
    curl \
    build-essential \
    intel-media-va-driver \
    vainfo \
    intel-gpu-tools \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p videos db transcripts

# Set default port (can be overridden by environment variable)
ENV BACKEND_INTERNAL_PORT=8000

# Expose port (using environment variable)
EXPOSE $BACKEND_INTERNAL_PORT

# Health check (using environment variable)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${BACKEND_INTERNAL_PORT}/health || exit 1

# Run the application (port will be read from environment in main.py)
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
