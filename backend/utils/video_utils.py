import cv2
import os
from typing import Dict, Optional, List
from moviepy.editor import VideoFile<PERSON>lip
import subprocess
from PIL import Image
import tempfile

def get_video_metadata(file_path: str) -> Dict:
    """Extract metadata from video file"""
    metadata = {}
    
    try:
        # Use OpenCV to get basic info
        cap = cv2.VideoCapture(file_path)
        if cap.isOpened():
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            
            # Calculate duration
            duration = frame_count / fps if fps > 0 else 0
            
            metadata.update({
                'width': width,
                'height': height,
                'fps': fps,
                'duration': duration,
                'frame_count': int(frame_count)
            })
            
            cap.release()
        
        # Use moviepy for more accurate duration if needed
        try:
            with VideoFileClip(file_path) as clip:
                metadata['duration'] = clip.duration
        except Exception:
            pass  # Use OpenCV duration as fallback
            
    except Exception as e:
        print(f"Error extracting metadata from {file_path}: {e}")
    
    return metadata

def generate_thumbnail(video_path: str, video_id: int) -> Optional[str]:
    """Generate thumbnail for video"""
    try:
        # Create thumbnails directory
        thumbnails_dir = os.path.join("videos", "thumbnails")
        os.makedirs(thumbnails_dir, exist_ok=True)
        
        # Output path
        thumbnail_path = os.path.join(thumbnails_dir, f"{video_id}.jpg")
        
        # Use OpenCV to extract frame
        cap = cv2.VideoCapture(video_path)
        if cap.isOpened():
            # Get frame from 10% into the video
            frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
            target_frame = int(frame_count * 0.1)
            
            cap.set(cv2.CAP_PROP_POS_FRAMES, target_frame)
            ret, frame = cap.read()
            
            if ret:
                # Resize frame to thumbnail size (320x180)
                height, width = frame.shape[:2]
                aspect_ratio = width / height
                
                if aspect_ratio > 16/9:  # Wide video
                    new_width = 320
                    new_height = int(320 / aspect_ratio)
                else:  # Tall or square video
                    new_height = 180
                    new_width = int(180 * aspect_ratio)
                
                resized_frame = cv2.resize(frame, (new_width, new_height))
                
                # Save thumbnail
                cv2.imwrite(thumbnail_path, resized_frame)
                cap.release()
                return thumbnail_path
            
            cap.release()
        
        # Fallback: use ffmpeg with GPU acceleration
        try:
            # Try Intel QSV hardware acceleration first
            subprocess.run([
                'ffmpeg', '-hwaccel', 'qsv', '-i', video_path, '-ss', '00:00:01.000',
                '-vframes', '1', '-vf', 'scale_qsv=320:180:force_original_aspect_ratio=decrease',
                '-c:v', 'mjpeg_qsv', thumbnail_path, '-y'
            ], check=True, capture_output=True)
            return thumbnail_path
        except subprocess.CalledProcessError:
            # Fallback to VAAPI
            try:
                subprocess.run([
                    'ffmpeg', '-hwaccel', 'vaapi', '-hwaccel_device', '/dev/dri/renderD128',
                    '-i', video_path, '-ss', '00:00:01.000',
                    '-vframes', '1', '-vf', 'scale_vaapi=320:180:force_original_aspect_ratio=decrease',
                    thumbnail_path, '-y'
                ], check=True, capture_output=True)
                return thumbnail_path
            except subprocess.CalledProcessError:
                # Final fallback to CPU
                try:
                    subprocess.run([
                        'ffmpeg', '-i', video_path, '-ss', '00:00:01.000',
                        '-vframes', '1', '-vf', 'scale=320:180:force_original_aspect_ratio=decrease',
                        thumbnail_path, '-y'
                    ], check=True, capture_output=True)
                    return thumbnail_path
                except subprocess.CalledProcessError:
                    pass
            
    except Exception as e:
        print(f"Error generating thumbnail for video {video_id}: {e}")
    
    return None

def extract_video_frames(video_path: str, num_frames: int = 8) -> list:
    """Extract evenly spaced frames from video for analysis"""
    frames = []
    
    try:
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return frames
        
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Calculate frame indices
        if frame_count <= num_frames:
            frame_indices = list(range(frame_count))
        else:
            step = frame_count // num_frames
            frame_indices = [i * step for i in range(num_frames)]
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret:
                frames.append(frame)
        
        cap.release()
        
    except Exception as e:
        print(f"Error extracting frames from {video_path}: {e}")
    
    return frames

def validate_video_file(file_path: str) -> bool:
    """Validate if file is a valid video"""
    try:
        cap = cv2.VideoCapture(file_path)
        if cap.isOpened():
            ret, _ = cap.read()
            cap.release()
            return ret
        return False
    except Exception:
        return False

def get_video_duration(file_path: str) -> float:
    """Get video duration in seconds"""
    try:
        with VideoFileClip(file_path) as clip:
            return clip.duration
    except Exception:
        try:
            cap = cv2.VideoCapture(file_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                duration = frame_count / fps if fps > 0 else 0
                cap.release()
                return duration
        except Exception:
            pass
    return 0.0

def check_gpu_acceleration_support() -> Dict[str, bool]:
    """Check what GPU acceleration methods are available"""
    support = {
        'qsv': False,
        'vaapi': False,
        'opencl': False,
        'drm': False
    }

    try:
        # Check QSV support
        result = subprocess.run([
            'ffmpeg', '-f', 'lavfi', '-i', 'testsrc2=duration=1:size=320x240:rate=1',
            '-c:v', 'h264_qsv', '-f', 'null', '-'
        ], capture_output=True, timeout=10)
        support['qsv'] = result.returncode == 0
    except:
        pass

    try:
        # Check VAAPI support
        result = subprocess.run([
            'ffmpeg', '-hwaccel', 'vaapi', '-hwaccel_device', '/dev/dri/renderD128',
            '-f', 'lavfi', '-i', 'testsrc2=duration=1:size=320x240:rate=1',
            '-c:v', 'h264_vaapi', '-f', 'null', '-'
        ], capture_output=True, timeout=10)
        support['vaapi'] = result.returncode == 0
    except:
        pass

    try:
        # Check if DRI devices exist
        support['drm'] = os.path.exists('/dev/dri/renderD128')
    except:
        pass

    return support

def get_optimal_ffmpeg_params(operation: str = 'thumbnail') -> List[str]:
    """Get optimal FFmpeg parameters based on available GPU acceleration"""
    gpu_support = check_gpu_acceleration_support()

    if operation == 'thumbnail':
        if gpu_support['qsv']:
            return ['-hwaccel', 'qsv', '-c:v', 'mjpeg_qsv']
        elif gpu_support['vaapi']:
            return ['-hwaccel', 'vaapi', '-hwaccel_device', '/dev/dri/renderD128']
        else:
            return []  # CPU fallback

    elif operation == 'transcode':
        if gpu_support['qsv']:
            return ['-hwaccel', 'qsv', '-c:v', 'h264_qsv']
        elif gpu_support['vaapi']:
            return ['-hwaccel', 'vaapi', '-hwaccel_device', '/dev/dri/renderD128', '-c:v', 'h264_vaapi']
        else:
            return ['-c:v', 'libx264']  # CPU fallback

    return []
