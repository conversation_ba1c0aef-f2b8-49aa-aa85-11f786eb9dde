"""
GPU Utilities for Intel Iris Xe Graphics optimization
"""
import subprocess
import os
import json
from typing import Dict, List, Optional, Tuple
import time

class GPUMonitor:
    """Monitor Intel GPU usage and performance"""
    
    def __init__(self):
        self.gpu_available = self._check_gpu_availability()
    
    def _check_gpu_availability(self) -> bool:
        """Check if Intel GPU is available and accessible"""
        try:
            # Check if DRI devices exist
            if not os.path.exists('/dev/dri/renderD128'):
                return False
            
            # Try to get GPU info
            result = subprocess.run(['intel_gpu_top', '-l'], 
                                  capture_output=True, timeout=2)
            return result.returncode == 0
        except:
            return False
    
    def get_gpu_usage(self) -> Dict[str, float]:
        """Get current GPU usage statistics"""
        if not self.gpu_available:
            return {'render': 0.0, 'video': 0.0, 'memory': 0.0}
        
        try:
            # Use intel_gpu_top to get usage stats
            result = subprocess.run(['intel_gpu_top', '-J', '-s', '1000'], 
                                  capture_output=True, timeout=3)
            if result.returncode == 0:
                data = json.loads(result.stdout.decode())
                return {
                    'render': data.get('engines', {}).get('Render/3D', {}).get('busy', 0.0),
                    'video': data.get('engines', {}).get('Video', {}).get('busy', 0.0),
                    'memory': data.get('memory', {}).get('resident', 0.0)
                }
        except:
            pass
        
        return {'render': 0.0, 'video': 0.0, 'memory': 0.0}
    
    def get_gpu_info(self) -> Dict[str, str]:
        """Get GPU information"""
        info = {
            'name': 'Intel Iris Xe Graphics',
            'driver': 'Unknown',
            'memory': 'Shared',
            'acceleration': []
        }
        
        try:
            # Get driver info
            result = subprocess.run(['vainfo'], capture_output=True)
            if result.returncode == 0:
                output = result.stdout.decode()
                if 'iHD driver' in output:
                    info['driver'] = 'Intel iHD (Hardware Acceleration)'
                    info['acceleration'].append('VAAPI')
        except:
            pass
        
        try:
            # Check QSV support
            result = subprocess.run(['ffmpeg', '-hwaccels'], capture_output=True)
            if result.returncode == 0:
                output = result.stdout.decode()
                if 'qsv' in output:
                    info['acceleration'].append('Intel QSV')
                if 'vaapi' in output:
                    info['acceleration'].append('VAAPI')
                if 'opencl' in output:
                    info['acceleration'].append('OpenCL')
        except:
            pass
        
        return info

class PerformanceOptimizer:
    """Optimize performance for Intel Iris Xe Graphics"""
    
    @staticmethod
    def get_optimal_whisper_settings() -> Dict[str, any]:
        """Get optimal Whisper settings for Intel GPU"""
        return {
            'model_size': 'base',  # Good balance for Intel GPU
            'device': 'cpu',  # Intel GPU doesn't support CUDA, use optimized CPU
            'fp16': False,  # Intel GPU works better with fp32
            'compute_type': 'int8'  # Quantization for better performance
        }
    
    @staticmethod
    def get_optimal_ffmpeg_params(operation: str, input_resolution: Tuple[int, int] = None) -> List[str]:
        """Get optimal FFmpeg parameters for Intel Iris Xe"""
        base_params = []
        
        if operation == 'thumbnail':
            # Optimized for thumbnail generation
            base_params = [
                '-hwaccel', 'qsv',
                '-c:v', 'mjpeg_qsv',
                '-preset', 'fast',
                '-quality', '85'
            ]
        
        elif operation == 'transcode':
            # Optimized for video transcoding
            base_params = [
                '-hwaccel', 'qsv',
                '-c:v', 'h264_qsv',
                '-preset', 'medium',
                '-b:v', '2M',
                '-maxrate', '4M',
                '-bufsize', '8M'
            ]
        
        elif operation == 'extract_audio':
            # Optimized for audio extraction
            base_params = [
                '-hwaccel', 'qsv',
                '-c:a', 'aac',
                '-b:a', '128k'
            ]
        
        return base_params
    
    @staticmethod
    def get_optimal_ollama_settings() -> Dict[str, any]:
        """Get optimal Ollama settings for Intel GPU"""
        return {
            'gpu_layers': 10,  # Conservative for Intel GPU
            'context_length': 2048,  # Balanced context size
            'batch_size': 512,  # Optimized batch size
            'threads': 4,  # Use multiple threads
            'memory_limit': '4GB'  # Conservative memory usage
        }

def benchmark_gpu_performance() -> Dict[str, float]:
    """Benchmark GPU performance for video processing tasks"""
    results = {
        'thumbnail_generation': 0.0,
        'video_decode': 0.0,
        'video_encode': 0.0
    }
    
    try:
        # Benchmark thumbnail generation
        start_time = time.time()
        result = subprocess.run([
            'ffmpeg', '-hwaccel', 'qsv', '-f', 'lavfi', 
            '-i', 'testsrc2=duration=1:size=1920x1080:rate=30',
            '-vframes', '1', '-c:v', 'mjpeg_qsv',
            '-f', 'null', '-'
        ], capture_output=True, timeout=10)
        
        if result.returncode == 0:
            results['thumbnail_generation'] = time.time() - start_time
    except:
        results['thumbnail_generation'] = float('inf')
    
    try:
        # Benchmark video encoding
        start_time = time.time()
        result = subprocess.run([
            'ffmpeg', '-hwaccel', 'qsv', '-f', 'lavfi',
            '-i', 'testsrc2=duration=5:size=1280x720:rate=30',
            '-c:v', 'h264_qsv', '-preset', 'fast',
            '-f', 'null', '-'
        ], capture_output=True, timeout=15)
        
        if result.returncode == 0:
            results['video_encode'] = time.time() - start_time
    except:
        results['video_encode'] = float('inf')
    
    return results

def get_gpu_optimization_report() -> Dict[str, any]:
    """Generate a comprehensive GPU optimization report"""
    monitor = GPUMonitor()
    optimizer = PerformanceOptimizer()
    
    report = {
        'gpu_available': monitor.gpu_available,
        'gpu_info': monitor.get_gpu_info(),
        'current_usage': monitor.get_gpu_usage(),
        'optimal_settings': {
            'whisper': optimizer.get_optimal_whisper_settings(),
            'ollama': optimizer.get_optimal_ollama_settings(),
            'ffmpeg_thumbnail': optimizer.get_optimal_ffmpeg_params('thumbnail'),
            'ffmpeg_transcode': optimizer.get_optimal_ffmpeg_params('transcode')
        },
        'performance_benchmark': benchmark_gpu_performance(),
        'recommendations': []
    }
    
    # Add recommendations based on findings
    if not monitor.gpu_available:
        report['recommendations'].append("GPU not detected or accessible. Check drivers and permissions.")
    
    if 'Intel QSV' not in report['gpu_info']['acceleration']:
        report['recommendations'].append("Intel QSV not available. Install intel-media-va-driver.")
    
    if report['performance_benchmark']['thumbnail_generation'] > 2.0:
        report['recommendations'].append("Thumbnail generation is slow. Consider using CPU fallback.")
    
    return report
