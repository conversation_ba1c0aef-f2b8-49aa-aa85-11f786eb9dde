# Fast build Dockerfile for development
# This version prioritizes build speed over optimization

FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=0

# Install system dependencies including complete Intel GPU support
RUN apt-get update && apt-get install -y \
    curl \
    ffmpeg \
    # Intel GPU drivers and tools
    intel-media-va-driver \
    vainfo \
    intel-gpu-tools \
    # Additional GPU support
    mesa-va-drivers \
    libva-drm2 \
    libva2 \
    && rm -rf /var/lib/apt/lists/*

# Add user to video group for GPU access
RUN groupadd -f video && usermod -a -G video root

# Set working directory
WORKDIR /app

# Copy and install core requirements first (for better caching)
COPY requirements.txt .
RUN pip install -r requirements.txt

# Copy and install ML requirements
COPY requirements-ml.txt .
RUN pip install -r requirements-ml.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p videos db transcripts logs

# Set default port
ENV BACKEND_INTERNAL_PORT=8000

# Expose port
EXPOSE $BACKEND_INTERNAL_PORT

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:${BACKEND_INTERNAL_PORT}/health || exit 1

# Run the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
