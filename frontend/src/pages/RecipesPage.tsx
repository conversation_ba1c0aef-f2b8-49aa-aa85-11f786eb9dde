import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ClockIcon,
  UserGroupIcon,
  CakeIcon,
  StarIcon,
  DocumentArrowDownIcon
} from '@heroicons/react/24/outline';
import { recipeApi, videoApi } from '../utils/api';
import { RecipeWithVideo } from '../types';
import LoadingSpinner from '../components/LoadingSpinner';
import ErrorMessage from '../components/ErrorMessage';

const RecipesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCuisine, setSelectedCuisine] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Fetch recipes with filters
  const { data: recipes, isLoading, error, refetch } = useQuery({
    queryKey: ['recipes-with-videos', searchTerm, selectedCuisine, selectedDifficulty],
    queryFn: () => recipeApi.getAllWithVideos({
      search: searchTerm || undefined,
      cuisine: selectedCuisine || undefined,
      difficulty: selectedDifficulty || undefined,
      limit: 50
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Fetch cuisines for filter
  const { data: cuisines } = useQuery({
    queryKey: ['recipe-cuisines'],
    queryFn: recipeApi.getCuisines,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });

  // Fetch difficulties for filter
  const { data: difficulties } = useQuery({
    queryKey: ['recipe-difficulties'],
    queryFn: recipeApi.getDifficulties,
    staleTime: 30 * 60 * 1000, // 30 minutes
  });

  const handleRecipeClick = (recipe: RecipeWithVideo) => {
    // Navigate to the video page that contains this recipe
    navigate(`/video/${recipe.video_id}`);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    refetch();
  };

  const clearFilters = () => {
    setSearchTerm('');
    setSelectedCuisine('');
    setSelectedDifficulty('');
  };

  const getDifficultyColor = (difficulty?: string) => {
    if (!difficulty) return 'bg-gray-100 text-gray-800';
    const lower = difficulty.toLowerCase();
    if (lower.includes('easy') || lower.includes('fácil')) return 'bg-green-100 text-green-800';
    if (lower.includes('medium') || lower.includes('medio')) return 'bg-yellow-100 text-yellow-800';
    if (lower.includes('hard') || lower.includes('difícil')) return 'bg-red-100 text-red-800';
    return 'bg-gray-100 text-gray-800';
  };

  const exportRecipe = async (recipe: RecipeWithVideo) => {
    // Create a simple text format for the recipe
    const recipeText = `
${recipe.title || 'Recipe'}
${recipe.description ? `\nDescription: ${recipe.description}` : ''}

${recipe.prep_time ? `Prep Time: ${recipe.prep_time}` : ''}
${recipe.cook_time ? `Cook Time: ${recipe.cook_time}` : ''}
${recipe.total_time ? `Total Time: ${recipe.total_time}` : ''}
${recipe.servings ? `Servings: ${recipe.servings}` : ''}
${recipe.difficulty ? `Difficulty: ${recipe.difficulty}` : ''}
${recipe.cuisine_type ? `Cuisine: ${recipe.cuisine_type}` : ''}

INGREDIENTS:
${recipe.ingredients.map((ing, index) => 
  `${index + 1}. ${ing.amount ? `${ing.amount} ${ing.unit || ''}`.trim() + ' ' : ''}${ing.name}${ing.notes ? ` (${ing.notes})` : ''}`
).join('\n')}

INSTRUCTIONS:
${recipe.instructions.map((step) => 
  `${step.step_number}. ${step.instruction}${step.time ? ` (${step.time})` : ''}${step.temperature ? ` at ${step.temperature}` : ''}`
).join('\n')}

Generated by tagTok - Video Recipe Extractor
    `.trim();

    // Create and download the file
    const blob = new Blob([recipeText], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${recipe.title || 'recipe'}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage message="Failed to load recipes" />;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          🍳 Recetas
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Descubre deliciosas recetas extraídas de videos de cocina
        </p>
      </div>

      {/* Search and Filters */}
      <div className="mb-8 space-y-4">
        {/* Search Bar */}
        <form onSubmit={handleSearch} className="flex gap-2">
          <div className="flex-1 relative">
            <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar recetas, ingredientes o cocina..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg 
                       bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                       focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            />
          </div>
          <button
            type="button"
            onClick={() => setShowFilters(!showFilters)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                     bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300
                     hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors
                     flex items-center gap-2"
          >
            <FunnelIcon className="h-5 w-5" />
            Filtros
          </button>
        </form>

        {/* Filters Panel */}
        {showFilters && (
          <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Tipo de Cocina
                </label>
                <select
                  value={selectedCuisine}
                  onChange={(e) => setSelectedCuisine(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                           bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                           focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Todas las Cocinas</option>
                  {cuisines?.map((cuisine) => (
                    <option key={cuisine} value={cuisine}>
                      {cuisine}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Dificultad
                </label>
                <select
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg
                           bg-white dark:bg-gray-800 text-gray-900 dark:text-white
                           focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                >
                  <option value="">Todas las Dificultades</option>
                  {difficulties?.map((difficulty) => (
                    <option key={difficulty} value={difficulty}>
                      {difficulty}
                    </option>
                  ))}
                </select>
              </div>

              <div className="flex items-end">
                <button
                  onClick={clearFilters}
                  className="w-full px-4 py-2 text-sm text-gray-600 dark:text-gray-400
                           hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  Limpiar Filtros
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Results Count */}
      <div className="mb-6">
        <p className="text-sm text-gray-600 dark:text-gray-400">
          {recipes?.length || 0} receta{recipes?.length !== 1 ? 's' : ''} encontrada{recipes?.length !== 1 ? 's' : ''}
        </p>
      </div>

      {/* Recipe Grid */}
      {recipes && recipes.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {recipes.map((recipe) => (
            <div
              key={recipe.id}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow
                       border border-gray-200 dark:border-gray-700 cursor-pointer group overflow-hidden"
              onClick={() => handleRecipeClick(recipe)}
            >
              {/* Video Thumbnail */}
              {recipe.video_thumbnail_path && (
                <div className="aspect-video w-full bg-gray-200 dark:bg-gray-700">
                  <img
                    src={videoApi.getThumbnailUrl(recipe.video_thumbnail_path.split('/').pop() || '')}
                    alt={recipe.title || 'Recipe thumbnail'}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      // Fallback to a placeholder if thumbnail fails to load
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      target.parentElement!.innerHTML = `
                        <div class="w-full h-full flex items-center justify-center bg-gray-200 dark:bg-gray-700">
                          <svg class="w-12 h-12 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                          </svg>
                        </div>
                      `;
                    }}
                  />
                </div>
              )}

              {/* Recipe Header */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-3">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors line-clamp-2">
                    {recipe.title || 'Receta Sin Título'}
                  </h3>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      exportRecipe(recipe);
                    }}
                    className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                    title="Exportar Receta"
                  >
                    <DocumentArrowDownIcon className="h-5 w-5" />
                  </button>
                </div>

                {recipe.description && (
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2">
                    {recipe.description}
                  </p>
                )}

                {/* Recipe Metadata */}
                <div className="space-y-2 mb-4">
                  {recipe.total_time && (
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <ClockIcon className="h-4 w-4 mr-2" />
                      {recipe.total_time}
                    </div>
                  )}
                  
                  {recipe.servings && (
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <UserGroupIcon className="h-4 w-4 mr-2" />
                      {recipe.servings}
                    </div>
                  )}

                  {recipe.cuisine_type && (
                    <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                      <CakeIcon className="h-4 w-4 mr-2" />
                      {recipe.cuisine_type}
                    </div>
                  )}
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2 mb-4">
                  {recipe.difficulty && (
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getDifficultyColor(recipe.difficulty)}`}>
                      {recipe.difficulty}
                    </span>
                  )}
                  
                  {recipe.extraction_confidence && (
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 flex items-center gap-1">
                      <StarIcon className="h-3 w-3" />
                      {Math.round(recipe.extraction_confidence * 100)}%
                    </span>
                  )}
                </div>

                {/* Recipe Stats */}
                <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                  <span>{recipe.ingredients.length} ingredientes</span>
                  <span>{recipe.instructions.length} pasos</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <CakeIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No se encontraron recetas
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            Intenta ajustar tus términos de búsqueda o filtros
          </p>
        </div>
      )}
    </div>
  );
};

export default RecipesPage;
