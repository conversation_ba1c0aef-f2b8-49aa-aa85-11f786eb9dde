import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { CogIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { analyticsApi } from '../utils/api';

interface ProcessingStats {
  processing_count: number;
  pending_count: number;
  failed_count: number;
}

const GlobalProcessingIndicator: React.FC = () => {
  const { data: stats, isLoading, error } = useQuery<ProcessingStats>({
    queryKey: ['processingStats'],
    queryFn: () => analyticsApi.getProcessingStatus(),
    refetchInterval: (query) => {
      // Stop polling if no videos are processing
      const data = query.state.data;
      if (data && data.processing_count === 0 && data.pending_count === 0) {
        return false;
      }
      // Poll every 10 seconds when there's processing activity
      return 10000;
    },
    retry: 1,
    staleTime: 5000,
  });

  // Don't show anything if there's no processing activity
  if (isLoading || error || !stats || (stats.processing_count === 0 && stats.pending_count === 0)) {
    return null;
  }

  const totalActive = stats.processing_count + stats.pending_count;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg flex items-center gap-2 max-w-sm">
        <CogIcon className="h-5 w-5 animate-spin" />
        <div className="text-sm">
          <div className="font-medium">
            Processing {totalActive} video{totalActive !== 1 ? 's' : ''}
          </div>
          {stats.failed_count > 0 && (
            <div className="flex items-center gap-1 text-red-200">
              <ExclamationTriangleIcon className="h-3 w-3" />
              <span className="text-xs">{stats.failed_count} failed</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlobalProcessingIndicator;
