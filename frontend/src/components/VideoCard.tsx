import React from 'react';
import { Link } from 'react-router-dom';
import {
  Play<PERSON>con,
  ClockIcon,
  TagIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { format } from 'date-fns';

import { Video } from '../types';
import { videoApi } from '../utils/api';
import { useVideoSelection } from '../contexts/VideoSelectionContext';
import ProcessingStatusIndicator from './ProcessingStatusIndicator';

interface VideoCardProps {
  video: Video;
}

const VideoCard: React.FC<VideoCardProps> = ({ video }) => {
  const {
    isSelectionMode,
    isVideoSelected,
    toggleVideoSelection
  } = useVideoSelection();

  const isSelected = isVideoSelected(video.id);

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const formatFileSize = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(1)} MB`;
  };

  const getStatusIcon = () => {
    switch (video.processing_status) {
      case 'completed':
        return <CheckCircleIcon className="h-4 w-4 text-green-500" />;
      case 'processing':
        return <ArrowPathIcon className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-4 w-4 text-red-500" />;
      default:
        return <ClockIcon className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusText = () => {
    switch (video.processing_status) {
      case 'completed':
        return 'Processed';
      case 'processing':
        return 'Processing...';
      case 'failed':
        return 'Failed';
      default:
        return 'Pending';
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    if (isSelectionMode) {
      e.preventDefault();
      toggleVideoSelection(video.id);
    }
  };

  const handleCheckboxClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    toggleVideoSelection(video.id);
  };

  return (
    <div
      className={`
        bg-white dark:bg-gray-800 rounded-lg shadow-md dark:shadow-gray-900/20
        overflow-hidden hover:shadow-lg dark:hover:shadow-gray-900/40
        transition-all duration-200 relative
        ${isSelected ? 'ring-2 ring-primary-500 dark:ring-primary-400' : ''}
        ${isSelectionMode ? 'cursor-pointer' : ''}
      `}
      onClick={handleCardClick}
    >
      {/* Selection Checkbox */}
      {isSelectionMode && (
        <div className="absolute top-2 left-2 z-10">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => {}} // Controlled by onClick
            onClick={handleCheckboxClick}
            className="w-5 h-5 text-primary-600 bg-white dark:bg-gray-700 border-gray-300 dark:border-gray-600 rounded focus:ring-primary-500 dark:focus:ring-primary-400 focus:ring-2"
          />
        </div>
      )}

      {/* Thumbnail */}
      <Link
        to={`/video/${video.id}`}
        className={`block relative ${isSelectionMode ? 'pointer-events-none' : ''}`}
      >
        <div className="aspect-video bg-gray-200 dark:bg-gray-700 relative overflow-hidden">
          {video.thumbnail_path ? (
            <img
              src={videoApi.getThumbnailUrl(video.thumbnail_path.split('/').pop() || '')}
              alt={video.title}
              className="w-full h-full object-cover"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = 'none';
              }}
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-600">
              <PlayIcon className="h-12 w-12 text-gray-400 dark:text-gray-300" />
            </div>
          )}
          
          {/* Play overlay */}
          <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
            <PlayIcon className="h-12 w-12 text-white opacity-0 hover:opacity-100 transition-opacity duration-200" />
          </div>
          
          {/* Duration badge */}
          {video.duration && (
            <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded">
              {formatDuration(video.duration)}
            </div>
          )}
          
          {/* Processing status badge */}
          <div className={`
            absolute bg-white dark:bg-gray-800 bg-opacity-90 dark:bg-opacity-90 text-xs px-2 py-1 rounded
            ${isSelectionMode ? 'top-2 right-2' : 'top-2 left-2'}
          `}>
            <ProcessingStatusIndicator
              videoId={video.id}
              showProgress={false}
              size="sm"
              className="text-xs"
            />
          </div>
        </div>
      </Link>

      {/* Content */}
      <div className="p-4">
        {/* Title */}
        <Link to={`/video/${video.id}`} className={isSelectionMode ? 'pointer-events-none' : ''}>
          <h3 className="font-medium text-gray-900 dark:text-white truncate hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
            {video.title || video.original_filename}
          </h3>
        </Link>

        {/* Metadata */}
        <div className="mt-2 space-y-1">
          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <ClockIcon className="h-4 w-4 mr-1" />
            <span>{format(new Date(video.upload_date), 'MMM d, yyyy')}</span>
          </div>

          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
            <span>{formatFileSize(video.file_size)}</span>
            {video.width && video.height && (
              <>
                <span className="mx-1">•</span>
                <span>{video.width}×{video.height}</span>
              </>
            )}
          </div>
        </div>

        {/* Tags */}
        {video.tags && video.tags.length > 0 && (
          <div className="mt-3">
            <div className="flex items-center mb-2">
              <TagIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 mr-1" />
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {video.tags.length} tag{video.tags.length !== 1 ? 's' : ''}
              </span>
            </div>
            <div className="flex flex-wrap gap-1">
              {video.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag.id}
                  className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-white"
                  style={{ backgroundColor: tag.color }}
                  title={tag.description}
                >
                  {tag.name}
                </span>
              ))}
              {video.tags.length > 3 && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                  +{video.tags.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}

        {/* Transcript preview */}
        {video.transcript && (
          <div className="mt-3">
            <p className="text-sm text-gray-600 line-clamp-2">
              {video.transcript.substring(0, 100)}
              {video.transcript.length > 100 && '...'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default VideoCard;
