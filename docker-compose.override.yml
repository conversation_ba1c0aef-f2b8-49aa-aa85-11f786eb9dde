# Development overrides for faster iteration
# This file is automatically loaded by docker-compose in development

services:
  backend:
    build:
      args:
        - INSTALL_DEV=true
    volumes:
      # Enable read-write mounting for development
      - ./backend:/app:rw
    environment:
      # Development-specific settings
      - MOUNT_SOURCE=rw
      - DEBUG=true
      - LOG_LEVEL=debug
      # Faster reload settings
      - WATCHDOG_ENABLED=true
    # Override healthcheck for faster startup in development
    healthcheck:
      interval: 15s
      timeout: 5s
      retries: 2
      start_period: 10s

  # Development database with faster settings
  # (if you want to add a separate dev database)
  
  # Optional: Add a development-only service for testing
  backend-test:
    extends:
      service: backend
    command: ["python", "-m", "pytest", "-v"]
    profiles:
      - testing
    depends_on:
      - backend
