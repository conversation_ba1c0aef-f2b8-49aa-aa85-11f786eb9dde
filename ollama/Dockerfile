# Custom Ollama image with Intel GPU support
FROM ollama/ollama:latest

# Install Intel GPU drivers and tools
RUN apt-get update && apt-get install -y \
    # Intel GPU drivers
    intel-media-va-driver \
    vainfo \
    intel-gpu-tools \
    # Additional GPU support
    mesa-va-drivers \
    libva-drm2 \
    libva2 \
    # OpenCL support for Intel GPU
    intel-opencl-icd \
    ocl-icd-opencl-dev \
    clinfo \
    && rm -rf /var/lib/apt/lists/*

# Add user to video group for GPU access
RUN groupadd -f video && usermod -a -G video root

# Set Intel GPU environment variables
ENV INTEL_GPU_ENABLED=1
ENV OLLAMA_INTEL_GPU=1

# Expose the default Ollama port
EXPOSE 11434

# Use the original Ollama entrypoint
ENTRYPOINT ["/bin/ollama"]
CMD ["serve"]
